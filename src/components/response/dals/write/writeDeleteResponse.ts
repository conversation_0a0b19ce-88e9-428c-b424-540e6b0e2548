import { responseModel } from '../../../response/models';
import { surveyModel } from '../../../survey/models';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';

export const writeDeleteResponse = async (responseId: string, accountId: string, reason: string) => {
  try {
    const response: any = await responseModel.findOne({
      where: {
        id: responseId,
      },
      include: [
        {
          model: surveyModel,
          attributes: ['accountId'],
          where: {
            account_id: accountId,
          },
          required: true,
        },
      ],
    });

    if (!response) {
      return {
        success: false,
        message: `${Var.app.emoji.failure} Response not found or you don't have permission`,
        payload: null,
      };
    }

    await response.update({
      is_deleted: true,
      delete_reason: reason,
    });

    await surveyModel.decrement('response_count', {
      where: { id: response.survey_id },
    });

    return {
      success: true,
      message: `${Var.app.emoji.success} Response deleted`,
      payload: response,
    };
  } catch (error) {
    logger.error('Error deleting response:', error);
    return {
      success: false,
      message: `${Var.app.emoji.failure} Could not delete response`,
      payload: error,
    };
  }
};
