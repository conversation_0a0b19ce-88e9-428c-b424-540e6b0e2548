import { Router } from 'express';
import {
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
} from '../../../../global/middlewares';
import { validateDeleteResponsePayload } from '../../middlewares';
import { GenerateApiVersionPath } from '../../../../global/helpers';
import { deleteResponseController } from '../../controllers';

export const deleteResponseRoute = Router();

deleteResponseRoute.delete(
  `${GenerateApiVersionPath()}responses/:responseId`,
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
  validateDeleteResponsePayload,
  deleteResponseController,
);
