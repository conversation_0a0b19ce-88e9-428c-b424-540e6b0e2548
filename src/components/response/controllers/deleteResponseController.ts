import { Request, Response } from 'express';
import { writeDeleteResponse } from '../dals';
import { verifyResourceOwnership } from '../../security/helpers';
import { Var } from '../../../global/var';
import { logger } from '../../../global/services';

export const deleteResponseController = async (req: Request, res: Response) => {
  const responseId = res.locals.responseId;
  const accountId = res.locals.accountId;
  const reason = res.locals.deleteReason;

  logger.info('Response delete attempt', {
    responseId: responseId.substring(0, 8) + '...',
    accountId: accountId.substring(0, 8) + '...',
    reason,
    ip: req.ip,
  });

  // SECURITY FIX: Double authorization check - middleware + controller level
  const isOwner = await verifyResourceOwnership('response', responseId, accountId);
  if (!isOwner) {
    logger.warn('Unauthorized response delete attempt', {
      responseId: responseId.substring(0, 8) + '...',
      accountId: accountId.substring(0, 8) + '...',
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });
    return res.status(403).json({
      success: false,
      message: 'Access denied',
    });
  }

  const result = await writeDeleteResponse(responseId, accountId, reason);
  if (!result.success) {
    return res.status(400).json({
      success: false,
      message: result.message,
    });
  }

  return res.status(200).json({
    success: true,
    message: `${Var.app.emoji.success} Response deleted`,
  });
};
