import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import { Var } from '../../../../global/var';

export const validateDeleteResponsePayload = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const schema = Joi.object({
      reason: Joi.string().max(255).optional(),
    });

    const { error, value } = schema.validate(req.body);

    if (error) {
      return res.status(400).json({
        success: false,
        message: `${Var.app.emoji.failure} Invalid request: ${error.message}`,
      });
    }

    res.locals.responseId = req.params.responseId;
    res.locals.deleteReason = value.reason;

    next();
  } catch (error) {
    console.error(`${Var.app.emoji.failure} Error validating delete response payload:`, error);
    return res.status(500).json({
      success: false,
      message: `${Var.app.emoji.failure} Server error while validating request`,
    });
  }
};
