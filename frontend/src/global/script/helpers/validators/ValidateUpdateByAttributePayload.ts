import Joi from 'joi';
import { UpdateByAttributePayloadInterface } from '../../interfaces';

const updatePayloadByAttributeSchema = Joi.object({
  attribute: Joi.string()
    .valid(
      'name',
      'email',
      'password',
      'title',
      'distribution',
      'embedUrl',
      'respondentDetails',
      'config',
    )
    .required(),
  value: Joi.when('attribute', {
    switch: [
      { is: 'name', then: Joi.string().required() },
      {
        is: 'email',
        then: Joi.string()
          .email({ tlds: { allow: false } })
          .min(5)
          .max(128)
          .lowercase()
          .trim()
          .required(),
      },
      { is: 'password', then: Joi.string().min(8).max(1024).required() },
      { is: 'title', then: Joi.string().required() },
      { is: 'distribution', then: Joi.string().required() },
      {
        is: 'embedUrl',
        then: Joi.string()
          .uri({ scheme: ['https', 'http'] })
          .regex(/^(https?:\/\/)?(localhost|127\.0\.0\.1)(:[0-9]+)?(\/.*)?$|^https:\/\/.*$/)
          .allow(''),
      },
      {
        is: 'respondentDetails',
        then: Joi.array()
          .items(
            Joi.object({
              label: Joi.string().required(),
              value: Joi.string().required(),
              inputType: Joi.string()
                .valid('text', 'email', 'dropdown', 'radio', 'checkbox', 'number')
                .required(),
              required: Joi.boolean().optional().required(),
              placeholder: Joi.string().optional().allow(''),
              options: Joi.when('inputType', {
                is: Joi.string().valid('dropdown', 'radio', 'checkbox'),
                then: Joi.array()
                  .items(
                    Joi.object({
                      value: Joi.string().required(),
                      label: Joi.string().required(),
                    }),
                  )
                  .optional(),
                otherwise: Joi.optional(),
              }),
            }),
          )
          .min(0)
          .max(1024)
          .required(),
      },
      { is: 'config', then: Joi.object().required() },
    ],
  }),
});

export const ValidateUpdateByAttributePayload = (
  updateByAttributePayload: UpdateByAttributePayloadInterface,
) => {
  let { error } = updatePayloadByAttributeSchema.validate(updateByAttributePayload);
  if (error) {
    return { isValid: false, validationMessage: `❌ ${error.details[0].message}` };
  } else {
    return { isValid: true, validationMessage: '' };
  }
};
