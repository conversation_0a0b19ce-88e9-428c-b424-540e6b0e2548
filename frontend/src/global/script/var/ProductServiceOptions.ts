/**
 * Global variables for product and service types and industries
 * Used in SensePrice, SenseChoice, and SensePriority survey creation
 */

import { SelectOption } from '../interfaces';

/**
 * Product types for survey targeting
 */
export const ProductTypes: SelectOption[] = [
  { value: '-', label: 'Select Type' },
  // Physical Products
  { value: 'consumer_electronics', label: 'Consumer Electronics' },
  { value: 'home_appliances', label: 'Home Appliances' },
  { value: 'clothing_apparel', label: 'Clothing & Apparel' },
  { value: 'food_beverages', label: 'Food & Beverages' },
  { value: 'health_beauty', label: 'Health & Beauty Products' },
  { value: 'sports_fitness', label: 'Sports & Fitness Equipment' },
  { value: 'automotive', label: 'Automotive Products' },
  { value: 'home_garden', label: 'Home & Garden Products' },
  { value: 'toys_games', label: 'Toys & Games' },
  { value: 'books_media', label: 'Books & Media' },
  { value: 'jewelry_accessories', label: 'Jewelry & Accessories' },
  { value: 'furniture', label: 'Furniture' },
  { value: 'tools_hardware', label: 'Tools & Hardware' },
  { value: 'pet_supplies', label: 'Pet Supplies' },
  { value: 'office_supplies', label: 'Office Supplies' },

  // Digital Products
  { value: 'software_applications', label: 'Software & Applications' },
  { value: 'mobile_apps', label: 'Mobile Apps' },
  { value: 'digital_content', label: 'Digital Content (eBooks, Music, Videos)' },
  { value: 'online_courses', label: 'Online Courses & Training' },
  { value: 'digital_tools', label: 'Digital Tools & Platforms' },
  { value: 'games_entertainment', label: 'Games & Digital Entertainment' },
  { value: 'subscriptions', label: 'Digital Subscriptions' },

  // Industrial/B2B Products
  { value: 'machinery_equipment', label: 'Machinery & Equipment' },
  { value: 'raw_materials', label: 'Raw Materials' },
  { value: 'components_parts', label: 'Components & Parts' },
  { value: 'industrial_supplies', label: 'Industrial Supplies' },
  { value: 'medical_devices', label: 'Medical Devices & Equipment' },

  // Other
  { value: 'other_product', label: 'Other Product Type' },
];

/**
 * Service types for survey targeting
 */
export const ServiceTypes: SelectOption[] = [
  { value: '-', label: 'Select Type' },
  // Professional Services
  { value: 'consulting', label: 'Consulting Services' },
  { value: 'legal_services', label: 'Legal Services' },
  { value: 'accounting_finance', label: 'Accounting & Financial Services' },
  { value: 'marketing_advertising', label: 'Marketing & Advertising' },
  { value: 'design_creative', label: 'Design & Creative Services' },
  { value: 'it_tech_support', label: 'IT & Technical Support' },
  { value: 'business_coaching', label: 'Business Coaching & Training' },
  { value: 'hr_recruitment', label: 'HR & Recruitment Services' },

  // Personal Services
  { value: 'healthcare_medical', label: 'Healthcare & Medical Services' },
  { value: 'education_tutoring', label: 'Education & Tutoring' },
  { value: 'fitness_wellness', label: 'Fitness & Wellness Services' },
  { value: 'beauty_personal_care', label: 'Beauty & Personal Care' },
  { value: 'home_maintenance', label: 'Home Maintenance & Repair' },
  { value: 'cleaning_services', label: 'Cleaning Services' },
  { value: 'pet_care', label: 'Pet Care Services' },
  { value: 'childcare', label: 'Childcare & Babysitting' },

  // Digital Services
  { value: 'web_development', label: 'Web Development' },
  { value: 'app_development', label: 'App Development' },
  { value: 'digital_marketing', label: 'Digital Marketing Services' },
  { value: 'content_creation', label: 'Content Creation' },
  { value: 'seo_services', label: 'SEO & Online Optimization' },
  { value: 'cloud_services', label: 'Cloud & Hosting Services' },
  { value: 'data_analytics', label: 'Data Analytics Services' },
  { value: 'cybersecurity', label: 'Cybersecurity Services' },

  // Transportation & Logistics
  { value: 'delivery_shipping', label: 'Delivery & Shipping' },
  { value: 'transportation', label: 'Transportation Services' },
  { value: 'logistics', label: 'Logistics & Supply Chain' },

  // Hospitality & Travel
  { value: 'accommodation', label: 'Accommodation & Lodging' },
  { value: 'travel_planning', label: 'Travel Planning & Booking' },
  { value: 'event_planning', label: 'Event Planning & Management' },
  { value: 'catering', label: 'Catering & Food Services' },

  // Financial Services
  { value: 'banking', label: 'Banking Services' },
  { value: 'insurance', label: 'Insurance Services' },
  { value: 'investment', label: 'Investment & Wealth Management' },
  { value: 'real_estate', label: 'Real Estate Services' },

  // Other
  { value: 'other_service', label: 'Other Service Type' },
];

/**
 * Industries for product targeting
 */
export const ProductIndustries: SelectOption[] = [
  { value: '-', label: 'Select Industry' },
  // Technology & Electronics
  { value: 'technology_software', label: 'Technology & Software' },
  { value: 'consumer_electronics', label: 'Consumer Electronics' },
  { value: 'telecommunications', label: 'Telecommunications' },
  { value: 'gaming_entertainment', label: 'Gaming & Digital Entertainment' },

  // Retail & Consumer Goods
  { value: 'retail_ecommerce', label: 'Retail & E-commerce' },
  { value: 'fashion_apparel', label: 'Fashion & Apparel' },
  { value: 'luxury_goods', label: 'Luxury Goods' },
  { value: 'sporting_goods', label: 'Sporting Goods' },

  // Health & Wellness
  { value: 'healthcare_medical', label: 'Healthcare & Medical' },
  { value: 'pharmaceuticals', label: 'Pharmaceuticals' },
  { value: 'fitness_wellness', label: 'Fitness & Wellness' },
  { value: 'beauty_cosmetics', label: 'Beauty & Cosmetics' },

  // Food & Beverage
  { value: 'food_beverage', label: 'Food & Beverage' },
  { value: 'organic_natural', label: 'Organic & Natural Products' },
  { value: 'supplements', label: 'Supplements & Nutrition' },

  // Home & Lifestyle
  { value: 'home_garden', label: 'Home & Garden' },
  { value: 'furniture_decor', label: 'Furniture & Home Decor' },
  { value: 'appliances', label: 'Home Appliances' },
  { value: 'pet_products', label: 'Pet Products' },

  // Automotive & Transportation
  { value: 'automotive', label: 'Automotive' },
  { value: 'transportation_logistics', label: 'Transportation & Logistics' },

  // Industrial & Manufacturing
  { value: 'manufacturing', label: 'Manufacturing' },
  { value: 'industrial_equipment', label: 'Industrial Equipment' },
  { value: 'construction_materials', label: 'Construction Materials' },
  { value: 'chemicals', label: 'Chemicals & Materials' },

  // Energy & Environment
  { value: 'energy_utilities', label: 'Energy & Utilities' },
  { value: 'renewable_energy', label: 'Renewable Energy' },
  { value: 'environmental', label: 'Environmental Products' },

  // Education & Media
  { value: 'education', label: 'Education & Training' },
  { value: 'books_publishing', label: 'Books & Publishing' },
  { value: 'media_entertainment', label: 'Media & Entertainment' },

  // Other
  { value: 'other_product_industry', label: 'Other Product Industry' },
];

/**
 * Industries for service targeting
 */
export const ServiceIndustries: SelectOption[] = [
  { value: '-', label: 'Select Industry' },
  // Professional Services
  { value: 'consulting', label: 'Consulting' },
  { value: 'legal_services', label: 'Legal Services' },
  { value: 'accounting_finance', label: 'Accounting & Finance' },
  { value: 'marketing_advertising', label: 'Marketing & Advertising' },
  { value: 'design_creative', label: 'Design & Creative Services' },

  // Technology Services
  { value: 'it_services', label: 'IT Services' },
  { value: 'software_development', label: 'Software Development' },
  { value: 'digital_marketing', label: 'Digital Marketing' },
  { value: 'cybersecurity', label: 'Cybersecurity' },
  { value: 'cloud_services', label: 'Cloud Services' },

  // Healthcare Services
  { value: 'healthcare', label: 'Healthcare' },
  { value: 'medical_services', label: 'Medical Services' },
  { value: 'mental_health', label: 'Mental Health & Wellness' },
  { value: 'dental_services', label: 'Dental Services' },
  { value: 'veterinary', label: 'Veterinary Services' },

  // Financial Services
  { value: 'banking', label: 'Banking' },
  { value: 'insurance', label: 'Insurance' },
  { value: 'investment', label: 'Investment Services' },
  { value: 'real_estate', label: 'Real Estate' },

  // Education & Training
  { value: 'education', label: 'Education' },
  { value: 'training_development', label: 'Training & Development' },
  { value: 'online_learning', label: 'Online Learning' },
  { value: 'coaching', label: 'Coaching & Mentoring' },

  // Personal Services
  { value: 'beauty_personal_care', label: 'Beauty & Personal Care' },
  { value: 'fitness_wellness', label: 'Fitness & Wellness' },
  { value: 'home_services', label: 'Home Services' },
  { value: 'childcare', label: 'Childcare & Education' },

  // Hospitality & Travel
  { value: 'hospitality', label: 'Hospitality' },
  { value: 'travel_tourism', label: 'Travel & Tourism' },
  { value: 'event_management', label: 'Event Management' },
  { value: 'food_service', label: 'Food Service' },

  // Transportation & Logistics
  { value: 'transportation', label: 'Transportation' },
  { value: 'logistics_shipping', label: 'Logistics & Shipping' },
  { value: 'delivery_services', label: 'Delivery Services' },

  // Maintenance & Repair
  { value: 'maintenance_repair', label: 'Maintenance & Repair' },
  { value: 'cleaning_services', label: 'Cleaning Services' },
  { value: 'security_services', label: 'Security Services' },

  // Government & Public
  { value: 'government', label: 'Government Services' },
  { value: 'nonprofit', label: 'Nonprofit Services' },
  { value: 'public_safety', label: 'Public Safety' },

  // Other
  { value: 'other_service_industry', label: 'Other Service Industry' },
];
