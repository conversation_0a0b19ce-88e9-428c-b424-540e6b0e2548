.centered {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 90vh;
  margin: 0 auto;
}

c-card {
  width: 90%;
  max-width: 480px;
}

e-spinner {
  margin-top: 10px;
}

ol {
  box-sizing: border-box;
  padding-left: 0;
  list-style-position: inside;
}

.survey-list {
  list-style-type: none;
  list-style-position: inside;
  padding-left: 0;
}

.survey-list li {
  border: var(--border);
  padding: calc(var(--padding) * 1.25);
  background: white;
  border-radius: var(--border-radius);
  margin-bottom: 1em;
  transition: all 0.25s;
}

.survey-list li:hover {
  cursor: pointer;
  border: 1px solid var(--color__grey--800);
  color: var(--color__grey--800);
}

.survey-list li:active {
  background: var(--color__grey--100);
}

.survey-list__response-date {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 200px;
}

.survey-list__row-item__1 {
  width: 15%;
}

.survey-list__row-item__2 {
  width: 60%;
}

.survey-list__row-item__3 {
  width: 15%;
}

#fab {
  position: fixed;
  right: 2em;
  bottom: 2em;
}

#ready-container {
  width: 90%;
  max-width: 480px;
  margin: 0 auto;
}

#error-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 100%;
  height: 50vh;
}
