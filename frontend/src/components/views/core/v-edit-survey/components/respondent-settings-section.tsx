import { Component, Prop, State, h, Listen, Event, EventEmitter } from '@stencil/core';
import {
  UpdateByAttributeApi,
  GenerateUpdateByAttributePayload,
} from '../../../../../global/script/helpers';
import {
  Var,
  RespondentDetailsOptions,
  RespondentDetailOption,
  FrontendLogger,
} from '../../../../../global/script/var';

@Component({
  tag: 'respondent-settings-section',
  styleUrl: '../v-edit-survey.css',
  shadow: true,
})
export class RespondentSettingsSection {
  @Prop() surveyId: string;
  @Prop() survey: any;

  @State() isUpdating: boolean = false;
  @State() updateMessage: string = '';
  @State() updateSuccess: boolean = false;
  @State() isCustomDetailModalOpen: boolean = false;
  @State() isEditMode: boolean = false;
  @State() editingDetail: RespondentDetailOption | null = null;
  @State() editingIndex: number = -1;
  @State() resetRespondentDetailSelectTrigger: number = 0;

  @Event({
    eventName: 'respondentDetailEditEvent',
    bubbles: true,
  })
  respondentDetailEditEventEmitter: EventEmitter;

  // Use the global RespondentDetailsOptions
  private respondentDetails = RespondentDetailsOptions;

  @Listen('selectChangeEvent')
  async handleSelectChangeEvent(event: CustomEvent) {
    if (event.detail.name === 'respondentDetailSelect' && event.detail.value !== '-') {
      const selectedValue = event.detail.value;
      const selectedLabel = event.detail.label;

      // If "Create Custom Detail" is selected, open the modal
      if (selectedValue === 'custom') {
        this.isEditMode = false;
        this.editingDetail = null;
        this.editingIndex = -1;
        this.isCustomDetailModalOpen = true;
        return;
      }

      // Find the selected option in RespondentDetailsOptions to get all properties
      const selectedOption = RespondentDetailsOptions.find(
        option => option.value === selectedValue,
      );

      // Check if this detail is already added
      const currentDetails = [...(this.survey.respondentDetails || [])];

      // Use a for loop to check if the detail already exists
      let alreadyExists = false;
      for (let i = 0; i < currentDetails.length; i++) {
        if (currentDetails[i].value === selectedValue) {
          alreadyExists = true;
          break;
        }
      }

      if (!alreadyExists) {
        const newDetail: RespondentDetailOption = {
          value: selectedValue,
          label: selectedLabel,
          inputType: selectedOption?.inputType || 'text',
          placeholder: selectedOption?.placeholder,
          options: selectedOption?.options,
          required: selectedOption?.required || false,
        };

        const updatedDetails = [...currentDetails, newDetail];
        await this.updateRespondentDetails(updatedDetails);
      }
    }
  }

  @Listen('addCustomRespondentDetail')
  async handleAddCustomRespondentDetail(event: CustomEvent) {
    const newDetail = event.detail;
    const isEditMode = this.isEditMode;

    const currentDetails = [...(this.survey.respondentDetails || [])];

    if (isEditMode && this.editingIndex >= 0) {
      // Update existing detail
      currentDetails[this.editingIndex] = newDetail;
      await this.updateRespondentDetails(currentDetails);
    } else {
      // Check if this detail is already added (for new details)
      let alreadyExists = false;
      for (let i = 0; i < currentDetails.length; i++) {
        if (currentDetails[i].value === newDetail.value) {
          alreadyExists = true;
          break;
        }
      }

      if (!alreadyExists) {
        const updatedDetails = [...currentDetails, newDetail];
        await this.updateRespondentDetails(updatedDetails);
      }
    }

    // Close the modal and reset edit state
    this.isCustomDetailModalOpen = false;
    this.isEditMode = false;
    this.editingDetail = null;
    this.editingIndex = -1;

    // Reset the dropdown to "Choose Respondent Details"
    this.resetRespondentDetailSelectTrigger = Date.now();
  }

  @Listen('modalCloseEvent')
  handleModalCloseEvent() {
    this.isCustomDetailModalOpen = false;
    this.isEditMode = false;
    this.editingDetail = null;
    this.editingIndex = -1;

    // Reset the dropdown to "Choose Respondent Details"
    this.resetRespondentDetailSelectTrigger = Date.now();
  }

  @Listen('respondentDetailDeleteEvent')
  async handleRespondentDetailDeleteEvent(event: CustomEvent) {
    await this.removeRespondentDetail(event.detail.value);
  }

  @Listen('listWithDeleteEvent')
  async handleListWithDeleteEvent(event: CustomEvent) {
    if (event.detail.name === 'deleteRespondentDetail') {
      await this.removeRespondentDetail(event.detail.value);
    }
  }

  // Remove a respondent detail
  private async removeRespondentDetail(valueToRemove: string) {
    const currentDetails = [...(this.survey.respondentDetails || [])];

    // Use a for loop to create a new array without the removed item
    let updatedDetails = [];
    for (let i = 0; i < currentDetails.length; i++) {
      if (currentDetails[i].value !== valueToRemove) {
        updatedDetails.push(currentDetails[i]);
      }
    }

    await this.updateRespondentDetails(updatedDetails);
  }

  // Handle edit detail
  private handleEditDetail(detail: RespondentDetailOption, index: number) {
    this.editingDetail = detail;
    this.editingIndex = index;
    this.isEditMode = true;
    this.isCustomDetailModalOpen = true;
  }

  // Update respondent details in the backend
  private async updateRespondentDetails(details: RespondentDetailOption[]) {
    this.isUpdating = true;
    this.updateMessage = '';
    this.updateSuccess = false;

    // Preserve all properties for each detail
    const formattedDetails = details.map(detail => ({
      value: detail.value,
      label: detail.label,
      inputType: detail.inputType || 'text',
      placeholder: detail.placeholder,
      options: detail.options,
      required: detail.required || false,
    }));

    const updatePayload = GenerateUpdateByAttributePayload('respondentDetails', formattedDetails);

    try {
      const { success, message } = await UpdateByAttributeApi(
        `${Var.api.endpoint.surveys}/${this.surveyId}`,
        updatePayload,
      );

      this.updateMessage = message;
      this.updateSuccess = success;

      if (success) {
        // Update local survey object to reflect changes
        this.survey.respondentDetails = details;
      }
    } catch (error) {
      this.updateMessage = 'An error occurred while updating respondent details';
      FrontendLogger.error('Error updating respondent details:', error);
    } finally {
      this.isUpdating = false;
    }
  }

  componentWillLoad() {
    // Ensure respondent details have all required properties
    if (this.survey?.respondentDetails?.length) {
      this.survey.respondentDetails = this.survey.respondentDetails.map(detail => ({
        value: detail.value,
        label: detail.label,
        inputType: detail.inputType || 'text',
        placeholder: detail.placeholder || '',
        options: detail.options || [],
        required: detail.required || false,
      }));
    }
  }

  render() {
    const hasRespondentDetails = this.survey?.respondentDetails?.length > 0;

    return (
      <div class="settings-section">
        <l-spacer value={3}></l-spacer>
        <e-text>
          <strong>What would you like to know about the survey respondents?</strong>
        </e-text>
        <l-spacer value={1}></l-spacer>

        {/* Dropdown selection with Create Custom Detail option */}
        <e-select
          name="respondentDetailSelect"
          options={JSON.stringify(this.respondentDetails)}
          resetTrigger={this.resetRespondentDetailSelectTrigger}
        ></e-select>
        <l-spacer value={2}></l-spacer>

        {/* Display selected respondent details */}
        {hasRespondentDetails ? (
          <div>
            <e-text variant="footnote">
              The following details will be collected from respondents
            </e-text>
            <l-spacer value={0.5}></l-spacer>
            {this.survey.respondentDetails.map((detail, index) => (
              <div>
                {index > 0 && <l-spacer value={2}></l-spacer>}
                <div class="respondent-detail-container">
                  <c-card>
                    <l-row justifyContent="space-between" align="baseline">
                      <div>
                        <e-text>
                          <strong>{detail.label}</strong>
                          {detail.required && <span class="required-badge"> * </span>}
                        </e-text>
                        <e-text variant="footnote">{detail.inputType.toUpperCase()}</e-text>
                      </div>
                      <div class="detail-actions">
                        <e-button
                          variant="link"
                          onClick={() => this.handleEditDetail(detail, index)}
                        >
                          <e-image
                            src="../../../assets/icon/dark/edit-dark.svg"
                            width="1.2em"
                          ></e-image>
                        </e-button>
                        <e-button
                          variant="link"
                          onClick={() => this.removeRespondentDetail(detail.value)}
                        >
                          <e-image
                            src="../../../assets/icon/red/trash-red.svg"
                            width="1.2em"
                          ></e-image>
                        </e-button>
                      </div>
                    </l-row>
                    {(detail.placeholder || (detail.options && detail.options.length > 0)) && (
                      <div>
                        <l-spacer value={1}></l-spacer>
                        <div class="detail-content">
                          {detail.placeholder && (
                            <div class="detail-section">
                              <e-text variant="footnote" class="detail-label">
                                PLACEHOLDER
                              </e-text>
                              <e-text>{detail.placeholder}</e-text>
                            </div>
                          )}

                          {detail.options && detail.options.length > 0 && (
                            <div>
                              {detail.placeholder && <l-spacer value={2}></l-spacer>}
                              <div class="detail-section">
                                <e-text variant="footnote" class="detail-label">
                                  OPTIONS
                                </e-text>
                                <ul class="options-list">
                                  {detail.options.map(option => (
                                    <li class="option-item">
                                      <e-text>{option.label}</e-text>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </c-card>
                </div>
              </div>
            ))}
            <l-spacer value={1}></l-spacer>
            <e-text variant="footnote">
              <span class="required-badge"> * </span> are required fields
            </e-text>
            <l-spacer value={2}></l-spacer>
          </div>
        ) : (
          <div>
            <e-text>No respondent details are currently being collected.</e-text>
            <l-spacer value={2}></l-spacer>
          </div>
        )}

        {this.isUpdating && (
          <div class="loading-indicator">
            <e-spinner theme="dark"></e-spinner>
            <l-spacer variant="horizontal" value={0.5}></l-spacer>
            <e-text variant="footnote">Updating...</e-text>
          </div>
        )}

        {this.updateMessage && !this.isUpdating && (
          <p-notification
            message={this.updateMessage}
            theme={this.updateSuccess ? 'success' : 'danger'}
          ></p-notification>
        )}

        {/* Modal for custom respondent detail */}
        <p-modal
          is-open={this.isCustomDetailModalOpen}
          modal-title={this.isEditMode ? 'Edit Respondent Detail' : 'Create Respondent Detail'}
        >
          {this.isCustomDetailModalOpen && (
            <p-respondent-detail-form
              editing-detail={this.editingDetail ? JSON.stringify(this.editingDetail) : ''}
              is-edit-mode={this.isEditMode}
            ></p-respondent-detail-form>
          )}
        </p-modal>
      </div>
    );
  }
}
