import Joi from 'joi';
import { createSurveyPayloadInterface } from '../../interfaces';

const createSurveyPayloadSchema = Joi.object({
  type: Joi.string()
    .valid('sensePrice', 'senseChoice', 'sensePoll', 'senseQuery', 'sensePriority')
    .required(),
  title: Joi.string().required(),
  distribution: Joi.string().required(),
  embedUrl: Joi.string()
    .uri({ scheme: ['https', 'http'] })
    .regex(/^(https?:\/\/)?(localhost|127\.0\.0\.1)(:[0-9]+)?(\/.*)?$|^https:\/\/.*$/)
    .allow(''),
  tags: Joi.array().items(Joi.string()).min(0).max(1024).required(),
  config: Joi.object().required(),
  respondentDetails: Joi.array()
    .items(
      Joi.object({
        label: Joi.string().required(),
        value: Joi.string().required(),
        inputType: Joi.string()
          .valid('text', 'email', 'dropdown', 'radio', 'checkbox', 'number')
          .required(),
        required: Joi.boolean().optional(),
        placeholder: Joi.string().optional().allow(''),
        options: Joi.when('inputType', {
          is: Joi.string().valid('dropdown', 'radio', 'checkbox'),
          then: Joi.array()
            .items(
              Joi.object({
                value: Joi.string().required(),
                label: Joi.string().required(),
              }),
            )
            .min(2)
            .optional(),
          otherwise: Joi.optional(),
        }),
      }),
    )
    .min(0)
    .max(1024)
    .required(),
});

export const validateCreateSurveyPayload = (payload: createSurveyPayloadInterface) => {
  let { error } = createSurveyPayloadSchema.validate(payload);
  if (error) {
    return {
      isValid: false,
      validationMessage: `❌ ${error.details[0].message}`,
    };
  } else {
    return { isValid: true, validationMessage: '' };
  }
};
