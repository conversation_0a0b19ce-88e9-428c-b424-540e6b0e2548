.priority-item {
  border: 1px solid var(--color-border);
  border-radius: 8px;
  padding: 1em;
  margin-bottom: 0.5em;
  background-color: var(--color-background);
  border-bottom: var(--border);
}

.item-content {
  width: 100%;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1em;
}

.item-actions {
  display: flex;
  gap: 0.5em;
  flex-shrink: 0;
}

.item-actions e-button {
  padding: 0.25em;
}

.item-description {
  margin-top: 0.5em;
  padding-left: 0;
}

.item-description e-text {
  color: var(--color-text-secondary);
  line-height: 1.4;
}
