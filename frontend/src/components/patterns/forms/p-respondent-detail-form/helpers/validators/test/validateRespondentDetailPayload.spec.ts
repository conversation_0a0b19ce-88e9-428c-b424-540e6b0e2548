import { validateRespondentDetailPayload } from '../validateRespondentDetailPayload';

describe('validateRespondentDetailPayload', () => {
  describe('Text input type validation', () => {
    it('should validate valid text input payload', () => {
      const payload = {
        label: 'Full Name',
        inputType: 'text',
        placeholder: 'Enter your full name',
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual({});
    });

    it('should require placeholder for text input type', () => {
      const payload = {
        label: 'Full Name',
        inputType: 'text',
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(false);
      expect(result.errors.placeholder).toBeDefined();
    });
  });

  describe('Email input type validation', () => {
    it('should validate valid email input payload', () => {
      const payload = {
        label: 'Email Address',
        inputType: 'email',
        placeholder: 'Enter your email',
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual({});
    });
  });

  describe('Dropdown input type validation', () => {
    it('should validate valid dropdown input payload with 2 options', () => {
      const payload = {
        label: 'Age Range',
        inputType: 'dropdown',
        options: [
          { value: '18_24', label: '18-24' },
          { value: '25_34', label: '25-34' },
        ],
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual({});
    });

    it('should validate valid dropdown input payload with more than 2 options', () => {
      const payload = {
        label: 'Age Range',
        inputType: 'dropdown',
        options: [
          { value: '18_24', label: '18-24' },
          { value: '25_34', label: '25-34' },
          { value: '35_44', label: '35-44' },
        ],
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual({});
    });

    it('should fail validation for dropdown with only 1 option', () => {
      const payload = {
        label: 'Age Range',
        inputType: 'dropdown',
        options: [{ value: '18_24', label: '18-24' }],
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(false);
      expect(result.errors.options).toContain('At least 2 options are required');
    });

    it('should fail validation for dropdown with no options', () => {
      const payload = {
        label: 'Age Range',
        inputType: 'dropdown',
        options: [],
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(false);
      expect(result.errors.options).toBeDefined();
    });

    it('should fail validation for dropdown without options property', () => {
      const payload = {
        label: 'Age Range',
        inputType: 'dropdown',
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(false);
      expect(result.errors.options).toBeDefined();
    });
  });

  describe('Radio input type validation', () => {
    it('should validate valid radio input payload with 2 options', () => {
      const payload = {
        label: 'Gender',
        inputType: 'radio',
        options: [
          { value: 'male', label: 'Male' },
          { value: 'female', label: 'Female' },
        ],
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual({});
    });

    it('should fail validation for radio with only 1 option', () => {
      const payload = {
        label: 'Gender',
        inputType: 'radio',
        options: [{ value: 'male', label: 'Male' }],
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(false);
      expect(result.errors.options).toContain('At least 2 options are required');
    });
  });

  describe('Checkbox input type validation', () => {
    it('should validate valid checkbox input payload with 2 options', () => {
      const payload = {
        label: 'Interests',
        inputType: 'checkbox',
        options: [
          { value: 'sports', label: 'Sports' },
          { value: 'music', label: 'Music' },
        ],
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual({});
    });

    it('should fail validation for checkbox with only 1 option', () => {
      const payload = {
        label: 'Interests',
        inputType: 'checkbox',
        options: [{ value: 'sports', label: 'Sports' }],
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(false);
      expect(result.errors.options).toContain('At least 2 options are required');
    });
  });

  describe('Number input type validation', () => {
    it('should validate valid number input payload', () => {
      const payload = {
        label: 'Age',
        inputType: 'number',
        placeholder: 'Enter your age',
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual({});
    });
  });

  describe('Label validation', () => {
    it('should fail validation for empty label', () => {
      const payload = {
        label: '',
        inputType: 'text',
        placeholder: 'Enter text',
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(false);
      expect(result.errors.label).toBeDefined();
    });

    it('should fail validation for missing label', () => {
      const payload = {
        inputType: 'text',
        placeholder: 'Enter text',
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(false);
      expect(result.errors.label).toBeDefined();
    });
  });

  describe('Input type validation', () => {
    it('should fail validation for invalid input type', () => {
      const payload = {
        label: 'Test Field',
        inputType: 'invalid',
        placeholder: 'Enter text',
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(false);
      expect(result.errors.inputType).toBeDefined();
    });
  });

  describe('Minimum options requirement edge cases', () => {
    it('should provide clear error message for dropdown with 1 option', () => {
      const payload = {
        label: 'Test Dropdown',
        inputType: 'dropdown',
        options: [{ value: 'option1', label: 'Option 1' }],
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(false);
      expect(result.errors.options).toContain('At least 2 options are required');
      expect(result.validationMessage).toContain('At least 2 options are required');
    });

    it('should provide clear error message for radio with 1 option', () => {
      const payload = {
        label: 'Test Radio',
        inputType: 'radio',
        options: [{ value: 'option1', label: 'Option 1' }],
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(false);
      expect(result.errors.options).toContain('At least 2 options are required');
    });

    it('should provide clear error message for checkbox with 1 option', () => {
      const payload = {
        label: 'Test Checkbox',
        inputType: 'checkbox',
        options: [{ value: 'option1', label: 'Option 1' }],
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(false);
      expect(result.errors.options).toContain('At least 2 options are required');
    });
  });

  describe('Placeholder handling for dropdown/radio/checkbox', () => {
    it('should validate dropdown without placeholder field', () => {
      const payload = {
        label: 'Test Dropdown',
        inputType: 'dropdown',
        options: [
          { value: 'option1', label: 'Option 1' },
          { value: 'option2', label: 'Option 2' },
        ],
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual({});
    });

    it('should allow dropdown with empty placeholder field', () => {
      const payload = {
        label: 'Test Dropdown',
        inputType: 'dropdown',
        placeholder: '',
        options: [
          { value: 'option1', label: 'Option 1' },
          { value: 'option2', label: 'Option 2' },
        ],
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual({});
    });

    it('should allow dropdown with non-empty placeholder field', () => {
      const payload = {
        label: 'Test Dropdown',
        inputType: 'dropdown',
        placeholder: 'Some placeholder text',
        options: [
          { value: 'option1', label: 'Option 1' },
          { value: 'option2', label: 'Option 2' },
        ],
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual({});
    });

    it('should allow radio with empty placeholder field', () => {
      const payload = {
        label: 'Test Radio',
        inputType: 'radio',
        placeholder: '',
        options: [
          { value: 'option1', label: 'Option 1' },
          { value: 'option2', label: 'Option 2' },
        ],
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual({});
    });

    it('should allow checkbox with empty placeholder field', () => {
      const payload = {
        label: 'Test Checkbox',
        inputType: 'checkbox',
        placeholder: '',
        options: [
          { value: 'option1', label: 'Option 1' },
          { value: 'option2', label: 'Option 2' },
        ],
        required: true,
      };

      const result = validateRespondentDetailPayload(payload);
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual({});
    });
  });
});
