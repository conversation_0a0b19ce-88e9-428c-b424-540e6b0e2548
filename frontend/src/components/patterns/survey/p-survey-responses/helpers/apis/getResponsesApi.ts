import { Var, FrontendLogger } from '../../../../../../global/script/var';
import { ApiWrapper } from '../../../../../../global/script/helpers/api/ApiWrapper';

export interface ResponseFilters {
  timeFilter?: string;
  customStartDate?: string;
  customEndDate?: string;
  statusFilter?: string;
  searchQuery?: string;
  page?: number;
  limit?: number;
}

/**
 * Fetches survey responses with filtering and pagination
 *
 * @param surveyId - The unique identifier of the survey
 * @param filters - Optional filters for responses
 * @returns Promise resolving to response data or error information
 */
export const getResponsesApi = async (surveyId: string, filters: ResponseFilters = {}) => {
  try {
    // Build query parameters
    const queryParams = new URLSearchParams();

    if (filters.timeFilter && filters.timeFilter !== 'all-time') {
      queryParams.append('timeFilter', filters.timeFilter);
    }

    if (filters.customStartDate) {
      queryParams.append('customStartDate', filters.customStartDate);
    }

    if (filters.customEndDate) {
      queryParams.append('customEndDate', filters.customEndDate);
    }

    if (filters.statusFilter && filters.statusFilter !== 'all') {
      queryParams.append('statusFilter', filters.statusFilter);
    }

    if (filters.searchQuery && filters.searchQuery.trim()) {
      queryParams.append('search', filters.searchQuery.trim());
    }

    if (filters.page) {
      queryParams.append('page', filters.page.toString());
    }

    if (filters.limit) {
      queryParams.append('limit', filters.limit.toString());
    }

    const endpoint = `${Var.api.endpoint.surveys}/${surveyId}/responses`;
    const url = queryParams.toString() ? `${endpoint}?${queryParams.toString()}` : endpoint;

    // DEBUG: Log response fetch attempt
    FrontendLogger.debug('Fetching survey responses', {
      surveyId,
      filters,
      endpoint: url,
    });

    const result = await ApiWrapper(url, {
      method: 'GET',
      includeCsrf: true, // Include CSRF token for authenticated requests
    });

    // DEBUG: Log response fetch result
    FrontendLogger.debug('Response fetch completed', {
      surveyId,
      success: result.success,
      responseCount: result.payload?.length || 0,
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    // DEBUG: Log response fetch error
    FrontendLogger.error('Error fetching survey responses', {
      surveyId,
      filters,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined,
    });

    return {
      success: false,
      message: 'Error fetching survey responses',
      payload: null,
    };
  }
};
