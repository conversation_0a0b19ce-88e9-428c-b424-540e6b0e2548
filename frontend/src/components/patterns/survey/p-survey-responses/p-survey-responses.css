/* Survey responses pattern styles */
:host {
  display: block;
  width: 100%;
  padding-bottom: 6em;
}

/* Skeleton loading animation */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

c-card {
  width: 100%;
}

.responses-table-placeholder {
  padding: 2em;
  text-align: center;
}

.responses-table-placeholder ul {
  text-align: left;
  max-width: 300px;
  margin: 0 auto;
}

/* Response Table Styles */
.response-table {
  width: 100%;
  border-collapse: collapse;
  min-height: 600px;
  display: flex;
  flex-direction: column;
}

.table-header {
  display: flex;
  font-weight: bold;
  padding-bottom: 1em;
  padding-left: 0.5em;
  align-items: center;
  flex-shrink: 0;
}

.table-body {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.table-row {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
  padding: 1em 0 0 0;
}

.table-row:hover {
  background-color: var(--color__grey--25);
}

.table-row--no-response {
  cursor: auto;
  padding-bottom: 0;
}

.table-row--no-response:hover {
  background-color: transparent;
}

.table-cell {
  flex: 1;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.table-cell-action {
  flex: none;
  width: auto;
  text-align: center;
}

.delete-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2em;
  padding: 0.25em;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  color: var(--color__red--600);
}

.delete-button:hover {
  background-color: var(--color__red--50);
}

/* Response Modal Content Styles */

.response-section {
  margin-bottom: 0;
}

.response-section pre {
  background-color: var(--color__grey--50);
  padding: 1em;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 0.9em;
  margin-top: 0.5em;
}

.status-badge {
  padding: 0.25em 0.5em;
  border-radius: 4px;
  font-size: 0.8em;
  font-weight: bold;
}

.status-badge.completed {
  background-color: var(--color__green--100);
  color: var(--color__green--800);
}

.status-badge.discarded {
  background-color: var(--color__red--100);
  color: var(--color__red--800);
}

/* Graph Container Styles */
#responses-graph {
  position: relative !important;
  overflow: hidden !important;
  z-index: 1 !important;
}

#responses-graph .plotly {
  position: relative !important;
  z-index: 1 !important;
}

#responses-graph .plotly .svg-container {
  position: relative !important;
  z-index: 1 !important;
  overflow: hidden !important;
}

#responses-graph .plotly .svg-container svg {
  position: relative !important;
  z-index: 1 !important;
  max-width: 100% !important;
  max-height: 100% !important;
}

/* Pagination Styles */
.pagination-controls {
  border-top: 1px solid var(--color__grey--200);
  padding-top: 1em;
}

.pagination-button {
  background: none;
  color: var(--color__indigo--600);
  border: none;
  padding: 0.5em 1em;
  cursor: pointer;
  font-size: 0.9em;
  transition: color 0.2s ease;
  margin: 0 0.5em;
}

.pagination-button:hover:not(:disabled) {
  color: var(--color__indigo--700);
}

.pagination-button:disabled {
  color: var(--color__grey--400);
  cursor: not-allowed;
  text-decoration: none;
}

.pagination-info {
  margin: 0 1em;
  font-size: 0.9em;
  color: var(--color__grey--700);
}

.pagination-number {
  background-color: white;
  color: var(--color__grey--600);
  border: var(--border);
  padding: 0.5em 0.75em;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
  margin: 0 0.25em;
  transition: all 0.2s ease;
  min-width: 40px;
}

.pagination-number:hover {
  border-color: var(--color__indigo--600);
  color: var(--color__indigo--600);
}

.pagination-number.active {
  background-color: var(--color__indigo--50);
  border-color: var(--color__indigo--600);
  color: var(--color__indigo--600);
}
