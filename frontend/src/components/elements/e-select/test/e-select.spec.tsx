import { newSpecPage } from '@stencil/core/testing';
import { ESelect } from '../e-select';

describe('e-select', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [ESelect],
      html: `<e-select></e-select>`,
    });
    expect(page.root).toEqualHtml(`
      <e-select>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </e-select>
    `);
  });

  it('should set selected value when value prop is provided', async () => {
    const options = [
      { value: 'text', label: 'Text' },
      { value: 'email', label: 'Email' },
      { value: 'dropdown', label: 'Dropdown' },
    ];

    const page = await newSpecPage({
      components: [ESelect],
      html: `<e-select options='${JSON.stringify(options)}' value="email" name="test"></e-select>`,
    });

    const component = page.rootInstance;
    expect(component.selectedValue).toBe('email');
  });

  it('should default to first option when no value prop is provided', async () => {
    const options = [
      { value: 'text', label: 'Text' },
      { value: 'email', label: 'Email' },
      { value: 'dropdown', label: 'Dropdown' },
    ];

    const page = await newSpecPage({
      components: [ESelect],
      html: `<e-select options='${JSON.stringify(options)}' name="test"></e-select>`,
    });

    const component = page.rootInstance;
    expect(component.selectedValue).toBe('text');
  });
});
